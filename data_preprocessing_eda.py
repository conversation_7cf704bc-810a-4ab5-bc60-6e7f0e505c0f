"""
Module: data_preprocessing_eda
Description: Data preprocessing and exploratory data analysis for QS Rankings manuscript
Author: Dr. <PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-03
Last Modified: 2025-01-03

Dependencies:
- pandas
- numpy
- matplotlib
- seaborn
- scipy
- scikit-learn
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.impute import KNNImputer
import warnings
warnings.filterwarnings('ignore')

def setup_visualization_style():
    """Set global styling for all visualizations."""
    plt.style.use('seaborn-v0_8-darkgrid')
    
    # Color palettes
    COLOR_PALETTE = {
        'categorical': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                        '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
        'sequential': sns.color_palette("viridis", 10),
        'diverging': sns.color_palette("RdBu_r", 10),
        'highlight': ['#cccccc', '#cccccc', '#cccccc', '#ff7f0e', '#cccccc']
    }
    
    # Typography and sizing
    plt.rcParams.update({
        'font.family': 'sans-serif',
        'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans'],
        'font.size': 12,
        'axes.titlesize': 16,
        'axes.titleweight': 'bold',
        'axes.labelsize': 14,
        'xtick.labelsize': 12,
        'ytick.labelsize': 12,
        'figure.figsize': (12, 8),
        'figure.dpi': 100
    })
    
    return COLOR_PALETTE

def load_and_clean_data(filepath):
    """
    Load and perform initial cleaning of the QS Rankings dataset.
    
    Parameters
    ----------
    filepath : str
        Path to the CSV file
        
    Returns
    -------
    DataFrame
        Cleaned dataset
    """
    # Load data
    df = pd.read_csv(filepath)
    
    # Handle 'NaN' strings (convert to actual NaN)
    df = df.replace('NaN', np.nan)
    
    # Convert numeric columns
    numeric_columns = [
        'Rank_2026', 'Academic_Reputation_Score', 'Academic_Reputation_Rank',
        'Employer_Reputation_Score', 'Employer_Reputation_Rank',
        'Faculty_Student_Score', 'Faculty_Student_Rank',
        'Citations_per_Faculty_Score', 'Citations_per_Faculty_Rank',
        'International_Faculty_Score', 'International_Faculty_Rank',
        'International_Students_Score', 'International_Students_Rank',
        'International_Students_Diversity_Score', 'International_Students_Diversity_Rank',
        'International_Research_Network_Score', 'International_Research_Network_Rank',
        'Employment_Outcomes_Score', 'Employment_Outcomes_Rank',
        'Sustainability_Score', 'Sustainability_Rank', 'Overall_Score'
    ]
    
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # Create derived variables
    df = create_derived_variables(df)
    
    return df

def create_derived_variables(df):
    """
    Create derived variables for analysis.
    
    Parameters
    ----------
    df : DataFrame
        Original dataset
        
    Returns
    -------
    DataFrame
        Dataset with derived variables
    """
    # International Diversity Composite Score
    diversity_scores = ['International_Students_Score', 'International_Faculty_Score', 
                       'International_Students_Diversity_Score', 'International_Research_Network_Score']
    
    # Calculate composite diversity score (mean of available scores)
    df['International_Diversity_Composite'] = df[diversity_scores].mean(axis=1, skipna=True)
    
    # Excellence Composite Score
    excellence_scores = ['Academic_Reputation_Score', 'Employer_Reputation_Score',
                        'Citations_per_Faculty_Score', 'Employment_Outcomes_Score']
    
    df['Excellence_Composite'] = df[excellence_scores].mean(axis=1, skipna=True)
    
    # Geographic diversity indicator
    country_counts = df['Country'].value_counts()
    df['Country_Frequency'] = df['Country'].map(country_counts)
    df['Geographic_Rarity'] = 1 / df['Country_Frequency']  # Higher values = more rare countries
    
    # Size categories
    size_mapping = {'S': 'Small', 'M': 'Medium', 'L': 'Large', 'XL': 'Extra Large'}
    df['Size_Category'] = df['Size'].map(size_mapping)
    
    # Focus categories
    focus_mapping = {'CO': 'Comprehensive', 'FC': 'Full Comprehensive', 'FO': 'Focused'}
    df['Focus_Category'] = df['Focus'].map(focus_mapping)
    
    # Research intensity categories
    research_mapping = {'VH': 'Very High', 'H': 'High', 'M': 'Medium', 'L': 'Low'}
    df['Research_Category'] = df['Research'].map(research_mapping)
    
    # Top tier indicator (top 100 universities)
    df['Top_Tier'] = (df['Rank_2026'] <= 100).astype(int)
    
    # Regional groupings
    region_mapping = {
        'United States': 'North America',
        'Canada': 'North America',
        'United Kingdom': 'Europe',
        'Germany': 'Europe',
        'France': 'Europe',
        'Netherlands': 'Europe',
        'Switzerland': 'Europe',
        'China (Mainland)': 'Asia',
        'Japan': 'Asia',
        'Singapore': 'Asia',
        'Hong Kong SAR, China': 'Asia',
        'Republic of Korea': 'Asia',
        'Australia': 'Oceania',
        'New Zealand': 'Oceania'
    }
    
    df['Region'] = df['Country'].map(region_mapping)
    df['Region'] = df['Region'].fillna('Other')
    
    return df

def handle_missing_values(df, strategy='knn'):
    """
    Handle missing values in the dataset.
    
    Parameters
    ----------
    df : DataFrame
        Dataset with missing values
    strategy : str
        Strategy for handling missing values ('knn', 'median', 'drop')
        
    Returns
    -------
    DataFrame
        Dataset with handled missing values
    """
    # Identify numeric columns for imputation
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    
    if strategy == 'knn':
        # Use KNN imputation for numeric columns
        imputer = KNNImputer(n_neighbors=5)
        df_numeric = df[numeric_cols].copy()
        df_imputed = pd.DataFrame(
            imputer.fit_transform(df_numeric),
            columns=numeric_cols,
            index=df.index
        )
        
        # Replace numeric columns with imputed values
        for col in numeric_cols:
            df[col] = df_imputed[col]
    
    elif strategy == 'median':
        # Use median imputation
        for col in numeric_cols:
            df[col] = df[col].fillna(df[col].median())
    
    # Handle categorical missing values
    categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
    for col in categorical_cols:
        df[col] = df[col].fillna(df[col].mode()[0] if not df[col].mode().empty else 'Unknown')
    
    return df

def exploratory_data_analysis(df, colors):
    """
    Conduct comprehensive exploratory data analysis.
    
    Parameters
    ----------
    df : DataFrame
        Cleaned dataset
    colors : dict
        Color palette for visualizations
        
    Returns
    -------
    dict
        Dictionary containing EDA results
    """
    eda_results = {}
    
    # 1. Dataset Overview
    print("=== EXPLORATORY DATA ANALYSIS ===")
    print(f"Dataset Shape: {df.shape}")
    print(f"Missing Values: {df.isnull().sum().sum()}")
    
    # 2. International Diversity Analysis
    print("\n=== INTERNATIONAL DIVERSITY METRICS ===")
    diversity_cols = ['International_Students_Score', 'International_Faculty_Score',
                     'International_Students_Diversity_Score', 'International_Research_Network_Score',
                     'International_Diversity_Composite']
    
    diversity_stats = df[diversity_cols].describe()
    print(diversity_stats)
    
    # 3. University Excellence Analysis
    print("\n=== UNIVERSITY EXCELLENCE METRICS ===")
    excellence_cols = ['Overall_Score', 'Academic_Reputation_Score', 'Employer_Reputation_Score',
                      'Citations_per_Faculty_Score', 'Employment_Outcomes_Score', 'Excellence_Composite']
    
    excellence_stats = df[excellence_cols].describe()
    print(excellence_stats)
    
    # 4. Geographic Distribution
    print("\n=== GEOGRAPHIC DISTRIBUTION ===")
    country_dist = df['Country'].value_counts().head(10)
    print("Top 10 Countries by University Count:")
    print(country_dist)
    
    region_dist = df['Region'].value_counts()
    print("\nRegional Distribution:")
    print(region_dist)
    
    # 5. Institutional Characteristics
    print("\n=== INSTITUTIONAL CHARACTERISTICS ===")
    print("Size Distribution:")
    print(df['Size_Category'].value_counts())
    
    print("\nFocus Distribution:")
    print(df['Focus_Category'].value_counts())
    
    print("\nResearch Intensity Distribution:")
    print(df['Research_Category'].value_counts())
    
    # Store results
    eda_results['diversity_stats'] = diversity_stats
    eda_results['excellence_stats'] = excellence_stats
    eda_results['country_distribution'] = country_dist
    eda_results['region_distribution'] = region_dist
    
    return eda_results

def create_correlation_analysis(df, colors):
    """
    Create correlation analysis between diversity and excellence metrics.
    
    Parameters
    ----------
    df : DataFrame
        Cleaned dataset
    colors : dict
        Color palette for visualizations
        
    Returns
    -------
    DataFrame
        Correlation matrix
    """
    # Select key variables for correlation analysis
    key_vars = [
        'International_Students_Score', 'International_Faculty_Score',
        'International_Students_Diversity_Score', 'International_Research_Network_Score',
        'International_Diversity_Composite',
        'Overall_Score', 'Academic_Reputation_Score', 'Employer_Reputation_Score',
        'Citations_per_Faculty_Score', 'Employment_Outcomes_Score', 'Excellence_Composite',
        'Rank_2026'
    ]
    
    # Calculate correlation matrix
    corr_matrix = df[key_vars].corr()
    
    # Create correlation heatmap
    plt.figure(figsize=(14, 12))
    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
    sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='RdBu_r', center=0,
                square=True, fmt='.3f', cbar_kws={"shrink": .8})
    plt.title('Correlation Matrix: International Diversity and University Excellence Metrics',
              fontsize=16, fontweight='bold', pad=20)
    plt.tight_layout()
    plt.savefig('correlation_matrix.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return corr_matrix

def main():
    """Main preprocessing and EDA function."""
    print("=== QS RANKINGS 2026: DATA PREPROCESSING & EDA ===")
    print("Author: Dr. Dharmendra Pandey, Symbiosis International (Deemed University)")
    print("=" * 70)
    
    # Setup visualization style
    colors = setup_visualization_style()
    
    # Load and clean data
    print("\n1. Loading and cleaning data...")
    df = load_and_clean_data('2026_QS_World_University_Rankings_STANDARDIZED_cleaned_with_visible_NaN.csv')
    print(f"Data loaded: {df.shape[0]} universities, {df.shape[1]} variables")
    
    # Handle missing values
    print("\n2. Handling missing values...")
    df_clean = handle_missing_values(df, strategy='median')  # Using median for simplicity
    print(f"Missing values after cleaning: {df_clean.isnull().sum().sum()}")
    
    # Exploratory data analysis
    print("\n3. Conducting exploratory data analysis...")
    eda_results = exploratory_data_analysis(df_clean, colors)
    
    # Correlation analysis
    print("\n4. Creating correlation analysis...")
    corr_matrix = create_correlation_analysis(df_clean, colors)
    
    # Save cleaned dataset
    df_clean.to_csv('QS_2026_cleaned_processed.csv', index=False)
    print("\n5. Cleaned dataset saved as 'QS_2026_cleaned_processed.csv'")
    
    print("\n" + "=" * 70)
    print("Data preprocessing and EDA completed successfully!")
    
    return df_clean, eda_results, corr_matrix

if __name__ == "__main__":
    df_clean, eda_results, corr_matrix = main()
