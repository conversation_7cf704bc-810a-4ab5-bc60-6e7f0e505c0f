"""
Module: data_analysis_setup
Description: Initial data analysis and project setup for QS Rankings manuscript
Author: Dr. <PERSON><PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-03
Last Modified: 2025-01-03

Dependencies:
- pandas
- numpy
- matplotlib
- seaborn
- scipy
- scikit-learn
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

# Set consistent styling
def setup_visualization_style():
    """Set global styling for all visualizations."""
    plt.style.use('seaborn-v0_8-darkgrid')
    
    # Color palettes for different visualization types
    COLOR_PALETTE = {
        'categorical': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                        '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
        'sequential': sns.color_palette("viridis", 10),
        'diverging': sns.color_palette("RdBu_r", 10),
        'highlight': ['#cccccc', '#cccccc', '#cccccc', '#ff7f0e', '#cccccc']
    }
    
    # Typography
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
    plt.rcParams['font.size'] = 12
    plt.rcParams['axes.titlesize'] = 16
    plt.rcParams['axes.titleweight'] = 'bold'
    plt.rcParams['axes.labelsize'] = 14
    plt.rcParams['xtick.labelsize'] = 12
    plt.rcParams['ytick.labelsize'] = 12
    
    # Figure sizing
    plt.rcParams['figure.figsize'] = (12, 8)
    plt.rcParams['figure.dpi'] = 100
    
    return COLOR_PALETTE

def load_dataset(filepath, file_type=None, **kwargs):
    """
    Standardized data loading function with error handling.
    
    Parameters
    ----------
    filepath : str
        Path to the data file
    file_type : str, optional
        Type of file ('csv', 'excel', 'json', etc.). If None, inferred from extension.
    **kwargs
        Additional parameters passed to the underlying loading function
        
    Returns
    -------
    DataFrame
        Loaded data in appropriate format
        
    Raises
    ------
    FileNotFoundError
        If file doesn't exist
    ValueError
        If file type is not supported
    """
    import os
    import logging
    
    if not os.path.exists(filepath):
        logging.error(f"File not found: {filepath}")
        raise FileNotFoundError(f"File not found: {filepath}")
    
    if file_type is None:
        file_type = filepath.split('.')[-1].lower()
    
    try:
        if file_type in ['csv', 'txt']:
            return pd.read_csv(filepath, **kwargs)
        elif file_type in ['xlsx', 'xls']:
            return pd.read_excel(filepath, **kwargs)
        elif file_type == 'json':
            return pd.read_json(filepath, **kwargs)
        elif file_type == 'parquet':
            return pd.read_parquet(filepath, **kwargs)
        else:
            logging.error(f"Unsupported file type: {file_type}")
            raise ValueError(f"Unsupported file type: {file_type}")
    except Exception as e:
        logging.error(f"Error loading data from {filepath}: {str(e)}")
        raise

def analyze_dataset_structure(df):
    """
    Comprehensive analysis of dataset structure and characteristics.
    
    Parameters
    ----------
    df : DataFrame
        The dataset to analyze
        
    Returns
    -------
    dict
        Dictionary containing analysis results
    """
    analysis = {}
    
    # Basic information
    analysis['shape'] = df.shape
    analysis['columns'] = list(df.columns)
    analysis['dtypes'] = df.dtypes.to_dict()
    
    # Missing values analysis
    missing_counts = df.isnull().sum()
    missing_percentages = (missing_counts / len(df)) * 100
    analysis['missing_data'] = {
        'counts': missing_counts.to_dict(),
        'percentages': missing_percentages.to_dict()
    }
    
    # Numerical columns analysis
    numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    analysis['numerical_columns'] = numerical_cols
    
    if numerical_cols:
        analysis['numerical_summary'] = df[numerical_cols].describe().to_dict()
    
    # Categorical columns analysis
    categorical_cols = df.select_dtypes(include=['object']).columns.tolist()
    analysis['categorical_columns'] = categorical_cols
    
    if categorical_cols:
        analysis['categorical_summary'] = {}
        for col in categorical_cols:
            analysis['categorical_summary'][col] = {
                'unique_count': df[col].nunique(),
                'unique_values': df[col].unique().tolist()[:10]  # First 10 unique values
            }
    
    return analysis

def identify_diversity_metrics(df):
    """
    Identify and categorize diversity-related metrics in the dataset.
    
    Parameters
    ----------
    df : DataFrame
        The QS rankings dataset
        
    Returns
    -------
    dict
        Dictionary categorizing diversity-related variables
    """
    diversity_metrics = {
        'international_students': [],
        'international_faculty': [],
        'international_research': [],
        'diversity_indices': [],
        'geographic_diversity': []
    }
    
    # Identify international student metrics
    for col in df.columns:
        if 'international_student' in col.lower():
            diversity_metrics['international_students'].append(col)
        elif 'international_faculty' in col.lower():
            diversity_metrics['international_faculty'].append(col)
        elif 'international_research' in col.lower():
            diversity_metrics['international_research'].append(col)
        elif 'diversity' in col.lower():
            diversity_metrics['diversity_indices'].append(col)
    
    # Geographic diversity (country-based)
    if 'Country' in df.columns:
        diversity_metrics['geographic_diversity'].append('Country')
    
    return diversity_metrics

def identify_excellence_metrics(df):
    """
    Identify university excellence metrics in the dataset.
    
    Parameters
    ----------
    df : DataFrame
        The QS rankings dataset
        
    Returns
    -------
    dict
        Dictionary categorizing excellence-related variables
    """
    excellence_metrics = {
        'overall_performance': [],
        'academic_reputation': [],
        'research_performance': [],
        'employment_outcomes': [],
        'sustainability': [],
        'ranking_positions': []
    }
    
    for col in df.columns:
        col_lower = col.lower()
        if 'overall_score' in col_lower or 'rank_2026' in col_lower:
            excellence_metrics['overall_performance'].append(col)
        elif 'academic_reputation' in col_lower:
            excellence_metrics['academic_reputation'].append(col)
        elif 'citation' in col_lower or 'research' in col_lower:
            excellence_metrics['research_performance'].append(col)
        elif 'employment' in col_lower:
            excellence_metrics['employment_outcomes'].append(col)
        elif 'sustainability' in col_lower:
            excellence_metrics['sustainability'].append(col)
        elif 'rank' in col_lower:
            excellence_metrics['ranking_positions'].append(col)
    
    return excellence_metrics

def main():
    """Main analysis function."""
    print("=== QS Rankings 2026 Dataset Analysis ===")
    print("Project: The Role of International Student Diversity in University Excellence")
    print("Author: Dr. Dharmendra Pandey, Symbiosis International (Deemed University)")
    print("=" * 70)
    
    # Setup visualization style
    COLORS = setup_visualization_style()
    
    # Load dataset
    print("\n1. Loading Dataset...")
    df = load_dataset('2026_QS_World_University_Rankings_STANDARDIZED_cleaned_with_visible_NaN.csv')
    print(f"Dataset loaded successfully: {df.shape[0]} universities, {df.shape[1]} variables")
    
    # Analyze dataset structure
    print("\n2. Analyzing Dataset Structure...")
    structure_analysis = analyze_dataset_structure(df)
    
    print(f"Dataset Shape: {structure_analysis['shape']}")
    print(f"Total Variables: {len(structure_analysis['columns'])}")
    print(f"Numerical Variables: {len(structure_analysis['numerical_columns'])}")
    print(f"Categorical Variables: {len(structure_analysis['categorical_columns'])}")
    
    # Missing data analysis
    print("\n3. Missing Data Analysis...")
    missing_data = structure_analysis['missing_data']
    high_missing = {k: v for k, v in missing_data['percentages'].items() if v > 10}
    if high_missing:
        print("Variables with >10% missing data:")
        for var, pct in sorted(high_missing.items(), key=lambda x: x[1], reverse=True):
            print(f"  {var}: {pct:.1f}%")
    else:
        print("No variables with >10% missing data")
    
    # Identify diversity metrics
    print("\n4. Identifying Diversity Metrics...")
    diversity_metrics = identify_diversity_metrics(df)
    for category, metrics in diversity_metrics.items():
        if metrics:
            print(f"  {category.replace('_', ' ').title()}: {metrics}")
    
    # Identify excellence metrics
    print("\n5. Identifying Excellence Metrics...")
    excellence_metrics = identify_excellence_metrics(df)
    for category, metrics in excellence_metrics.items():
        if metrics:
            print(f"  {category.replace('_', ' ').title()}: {metrics}")
    
    print("\n" + "=" * 70)
    print("Initial analysis completed. Ready for detailed statistical analysis.")
    
    return df, structure_analysis, diversity_metrics, excellence_metrics

if __name__ == "__main__":
    df, structure_analysis, diversity_metrics, excellence_metrics = main()
