"""
Module: advanced_statistical_analysis
Description: Advanced statistical analysis for international diversity and university excellence
Author: Dr. <PERSON>, Symbiosis International (Deemed University)
Created: 2025-01-03
Last Modified: 2025-01-03

Dependencies:
- pandas
- numpy
- matplotlib
- seaborn
- scipy
- scikit-learn
- statsmodels
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import r2_score, mean_squared_error
import statsmodels.api as sm
from statsmodels.stats.mediation import Mediation
import warnings
warnings.filterwarnings('ignore')

def setup_visualization_style():
    """Set global styling for all visualizations."""
    plt.style.use('seaborn-v0_8-darkgrid')
    
    COLOR_PALETTE = {
        'categorical': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                        '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
        'sequential': sns.color_palette("viridis", 10),
        'diverging': sns.color_palette("RdBu_r", 10),
        'highlight': ['#cccccc', '#cccccc', '#cccccc', '#ff7f0e', '#cccccc']
    }
    
    plt.rcParams.update({
        'font.family': 'sans-serif',
        'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans'],
        'font.size': 12,
        'axes.titlesize': 16,
        'axes.titleweight': 'bold',
        'axes.labelsize': 14,
        'xtick.labelsize': 12,
        'ytick.labelsize': 12,
        'figure.figsize': (12, 8),
        'figure.dpi': 100
    })
    
    return COLOR_PALETTE

def load_processed_data():
    """Load the preprocessed dataset."""
    try:
        df = pd.read_csv('QS_2026_cleaned_processed.csv')
        print(f"Processed data loaded: {df.shape[0]} universities, {df.shape[1]} variables")
        return df
    except FileNotFoundError:
        print("Processed data file not found. Please run data preprocessing first.")
        return None

def correlation_analysis(df):
    """
    Comprehensive correlation analysis between diversity and excellence metrics.
    
    Parameters
    ----------
    df : DataFrame
        Processed dataset
        
    Returns
    -------
    dict
        Dictionary containing correlation results
    """
    print("\n=== CORRELATION ANALYSIS ===")
    
    # Define key variables
    diversity_vars = [
        'International_Students_Score', 'International_Faculty_Score',
        'International_Students_Diversity_Score', 'International_Research_Network_Score',
        'International_Diversity_Composite'
    ]
    
    excellence_vars = [
        'Overall_Score', 'Academic_Reputation_Score', 'Employer_Reputation_Score',
        'Citations_per_Faculty_Score', 'Employment_Outcomes_Score', 'Excellence_Composite'
    ]
    
    # Calculate correlations
    correlations = {}
    for div_var in diversity_vars:
        correlations[div_var] = {}
        for exc_var in excellence_vars:
            corr_coef, p_value = stats.pearsonr(df[div_var], df[exc_var])
            correlations[div_var][exc_var] = {
                'correlation': corr_coef,
                'p_value': p_value,
                'significant': p_value < 0.05
            }
    
    # Print significant correlations
    print("Significant correlations (p < 0.05):")
    for div_var in diversity_vars:
        for exc_var in excellence_vars:
            result = correlations[div_var][exc_var]
            if result['significant']:
                print(f"{div_var} - {exc_var}: r = {result['correlation']:.3f}, p = {result['p_value']:.3f}")
    
    return correlations

def regression_analysis(df):
    """
    Multiple regression analysis examining diversity-excellence relationships.
    
    Parameters
    ----------
    df : DataFrame
        Processed dataset
        
    Returns
    -------
    dict
        Dictionary containing regression results
    """
    print("\n=== REGRESSION ANALYSIS ===")
    
    # Prepare variables
    diversity_predictors = [
        'International_Students_Score', 'International_Faculty_Score',
        'International_Students_Diversity_Score', 'International_Research_Network_Score'
    ]
    
    control_vars = ['Faculty_Student_Score']  # Control for faculty-student ratio
    
    excellence_outcomes = [
        'Overall_Score', 'Academic_Reputation_Score', 'Employer_Reputation_Score',
        'Citations_per_Faculty_Score', 'Employment_Outcomes_Score'
    ]
    
    regression_results = {}
    
    for outcome in excellence_outcomes:
        print(f"\nRegression Analysis: {outcome}")
        
        # Prepare data
        X_vars = diversity_predictors + control_vars
        X = df[X_vars].dropna()
        y = df.loc[X.index, outcome]
        
        # Add constant for statsmodels
        X_sm = sm.add_constant(X)
        
        # Fit regression model
        model = sm.OLS(y, X_sm).fit()
        
        # Store results
        regression_results[outcome] = {
            'model': model,
            'r_squared': model.rsquared,
            'adj_r_squared': model.rsquared_adj,
            'f_statistic': model.fvalue,
            'f_pvalue': model.f_pvalue,
            'coefficients': model.params.to_dict(),
            'p_values': model.pvalues.to_dict(),
            'significant_predictors': [var for var in X_vars if model.pvalues[var] < 0.05]
        }
        
        # Print summary
        print(f"R² = {model.rsquared:.3f}, Adj. R² = {model.rsquared_adj:.3f}")
        print(f"F-statistic = {model.fvalue:.3f}, p = {model.f_pvalue:.3f}")
        print("Significant predictors:", regression_results[outcome]['significant_predictors'])
    
    return regression_results

def cluster_analysis(df):
    """
    Cluster analysis to identify patterns in diversity-excellence relationships.
    
    Parameters
    ----------
    df : DataFrame
        Processed dataset
        
    Returns
    -------
    dict
        Dictionary containing cluster analysis results
    """
    print("\n=== CLUSTER ANALYSIS ===")
    
    # Select variables for clustering
    cluster_vars = [
        'International_Students_Score', 'International_Faculty_Score',
        'International_Students_Diversity_Score', 'International_Research_Network_Score',
        'Overall_Score', 'Academic_Reputation_Score', 'Employment_Outcomes_Score'
    ]
    
    # Prepare data
    X = df[cluster_vars].dropna()
    
    # Standardize variables
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Determine optimal number of clusters using elbow method
    inertias = []
    k_range = range(2, 11)
    
    for k in k_range:
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        kmeans.fit(X_scaled)
        inertias.append(kmeans.inertia_)
    
    # Fit final model with optimal k (let's use k=4 for interpretability)
    optimal_k = 4
    kmeans_final = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
    cluster_labels = kmeans_final.fit_predict(X_scaled)
    
    # Add cluster labels to dataframe
    df_clustered = df.loc[X.index].copy()
    df_clustered['Cluster'] = cluster_labels
    
    # Analyze cluster characteristics
    cluster_summary = df_clustered.groupby('Cluster')[cluster_vars].mean()
    
    print(f"Identified {optimal_k} clusters:")
    print(cluster_summary)
    
    # Cluster interpretation
    cluster_interpretations = {}
    for i in range(optimal_k):
        cluster_data = cluster_summary.loc[i]
        interpretation = f"Cluster {i}: "
        
        # Diversity characteristics
        if cluster_data['International_Students_Score'] > cluster_summary['International_Students_Score'].mean():
            interpretation += "High international student diversity, "
        else:
            interpretation += "Low international student diversity, "
        
        # Excellence characteristics
        if cluster_data['Overall_Score'] > cluster_summary['Overall_Score'].mean():
            interpretation += "High overall excellence"
        else:
            interpretation += "Low overall excellence"
        
        cluster_interpretations[i] = interpretation
        print(interpretation)
    
    return {
        'cluster_labels': cluster_labels,
        'cluster_summary': cluster_summary,
        'cluster_interpretations': cluster_interpretations,
        'clustered_data': df_clustered
    }

def mediation_analysis(df):
    """
    Mediation analysis examining indirect effects through international faculty.
    
    Parameters
    ----------
    df : DataFrame
        Processed dataset
        
    Returns
    -------
    dict
        Dictionary containing mediation analysis results
    """
    print("\n=== MEDIATION ANALYSIS ===")
    
    # Define mediation model: International Students -> International Faculty -> Excellence
    X = df['International_Students_Score'].dropna()
    M = df.loc[X.index, 'International_Faculty_Score']
    Y = df.loc[X.index, 'Overall_Score']
    
    # Remove any remaining NaN values
    valid_idx = ~(X.isna() | M.isna() | Y.isna())
    X = X[valid_idx]
    M = M[valid_idx]
    Y = Y[valid_idx]
    
    print(f"Mediation analysis with {len(X)} observations")
    
    # Path a: X -> M
    model_a = sm.OLS(M, sm.add_constant(X)).fit()
    path_a = model_a.params[1]
    path_a_pvalue = model_a.pvalues[1]
    
    # Path b: M -> Y (controlling for X)
    X_M = pd.DataFrame({'X': X, 'M': M})
    model_b = sm.OLS(Y, sm.add_constant(X_M)).fit()
    path_b = model_b.params['M']
    path_b_pvalue = model_b.pvalues['M']
    
    # Path c: X -> Y (total effect)
    model_c = sm.OLS(Y, sm.add_constant(X)).fit()
    path_c = model_c.params[1]
    path_c_pvalue = model_c.pvalues[1]
    
    # Path c': X -> Y (direct effect, controlling for M)
    path_c_prime = model_b.params['X']
    path_c_prime_pvalue = model_b.pvalues['X']
    
    # Indirect effect
    indirect_effect = path_a * path_b
    
    # Proportion mediated
    proportion_mediated = indirect_effect / path_c if path_c != 0 else 0
    
    print(f"Path a (X -> M): β = {path_a:.3f}, p = {path_a_pvalue:.3f}")
    print(f"Path b (M -> Y|X): β = {path_b:.3f}, p = {path_b_pvalue:.3f}")
    print(f"Path c (X -> Y): β = {path_c:.3f}, p = {path_c_pvalue:.3f}")
    print(f"Path c' (X -> Y|M): β = {path_c_prime:.3f}, p = {path_c_prime_pvalue:.3f}")
    print(f"Indirect effect: {indirect_effect:.3f}")
    print(f"Proportion mediated: {proportion_mediated:.3f}")
    
    return {
        'path_a': path_a,
        'path_b': path_b,
        'path_c': path_c,
        'path_c_prime': path_c_prime,
        'indirect_effect': indirect_effect,
        'proportion_mediated': proportion_mediated,
        'path_a_pvalue': path_a_pvalue,
        'path_b_pvalue': path_b_pvalue,
        'path_c_pvalue': path_c_pvalue,
        'path_c_prime_pvalue': path_c_prime_pvalue
    }

def create_publication_visualizations(df, analysis_results, colors):
    """
    Create publication-quality visualizations for the manuscript.

    Parameters
    ----------
    df : DataFrame
        Processed dataset
    analysis_results : dict
        Results from statistical analyses
    colors : dict
        Color palette for visualizations
    """
    print("\n=== CREATING PUBLICATION VISUALIZATIONS ===")

    # Figure 1: Correlation Heatmap
    plt.figure(figsize=(14, 10))
    diversity_vars = ['International_Students_Score', 'International_Faculty_Score',
                     'International_Students_Diversity_Score', 'International_Research_Network_Score']
    excellence_vars = ['Overall_Score', 'Academic_Reputation_Score', 'Employer_Reputation_Score',
                      'Citations_per_Faculty_Score', 'Employment_Outcomes_Score']

    corr_subset = df[diversity_vars + excellence_vars].corr()
    mask = np.triu(np.ones_like(corr_subset, dtype=bool))
    sns.heatmap(corr_subset, mask=mask, annot=True, cmap='RdBu_r', center=0,
                square=True, fmt='.3f', cbar_kws={"shrink": .8})
    plt.title('Figure 1: Correlation Matrix - International Diversity and University Excellence Metrics',
              fontsize=14, fontweight='bold', pad=20)
    plt.tight_layout()
    plt.savefig('Figure1_Correlation_Matrix.png', dpi=300, bbox_inches='tight')
    plt.close()

    # Figure 2: Scatter Plot with Regression Line
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Figure 2: Relationships Between International Diversity and University Excellence',
                 fontsize=16, fontweight='bold')

    # Plot 1: International Students vs Overall Score
    axes[0,0].scatter(df['International_Students_Score'], df['Overall_Score'],
                     alpha=0.6, color=colors['categorical'][0])
    z = np.polyfit(df['International_Students_Score'], df['Overall_Score'], 1)
    p = np.poly1d(z)
    axes[0,0].plot(df['International_Students_Score'], p(df['International_Students_Score']),
                   "r--", alpha=0.8)
    axes[0,0].set_xlabel('International Students Score')
    axes[0,0].set_ylabel('Overall Score')
    axes[0,0].set_title('A) International Students vs Overall Excellence')

    # Plot 2: International Faculty vs Academic Reputation
    axes[0,1].scatter(df['International_Faculty_Score'], df['Academic_Reputation_Score'],
                     alpha=0.6, color=colors['categorical'][1])
    z = np.polyfit(df['International_Faculty_Score'], df['Academic_Reputation_Score'], 1)
    p = np.poly1d(z)
    axes[0,1].plot(df['International_Faculty_Score'], p(df['International_Faculty_Score']),
                   "r--", alpha=0.8)
    axes[0,1].set_xlabel('International Faculty Score')
    axes[0,1].set_ylabel('Academic Reputation Score')
    axes[0,1].set_title('B) International Faculty vs Academic Reputation')

    # Plot 3: Diversity Composite vs Excellence Composite
    axes[1,0].scatter(df['International_Diversity_Composite'], df['Excellence_Composite'],
                     alpha=0.6, color=colors['categorical'][2])
    z = np.polyfit(df['International_Diversity_Composite'], df['Excellence_Composite'], 1)
    p = np.poly1d(z)
    axes[1,0].plot(df['International_Diversity_Composite'], p(df['International_Diversity_Composite']),
                   "r--", alpha=0.8)
    axes[1,0].set_xlabel('International Diversity Composite')
    axes[1,0].set_ylabel('Excellence Composite')
    axes[1,0].set_title('C) Composite Diversity vs Composite Excellence')

    # Plot 4: Research Network vs Citations
    axes[1,1].scatter(df['International_Research_Network_Score'], df['Citations_per_Faculty_Score'],
                     alpha=0.6, color=colors['categorical'][3])
    z = np.polyfit(df['International_Research_Network_Score'], df['Citations_per_Faculty_Score'], 1)
    p = np.poly1d(z)
    axes[1,1].plot(df['International_Research_Network_Score'], p(df['International_Research_Network_Score']),
                   "r--", alpha=0.8)
    axes[1,1].set_xlabel('International Research Network Score')
    axes[1,1].set_ylabel('Citations per Faculty Score')
    axes[1,1].set_title('D) Research Networks vs Research Impact')

    plt.tight_layout()
    plt.savefig('Figure2_Scatter_Plots.png', dpi=300, bbox_inches='tight')
    plt.close()

    # Figure 3: Cluster Analysis Visualization
    plt.figure(figsize=(12, 8))
    cluster_data = analysis_results['clusters']['clustered_data']
    scatter = plt.scatter(cluster_data['International_Diversity_Composite'],
                         cluster_data['Overall_Score'],
                         c=cluster_data['Cluster'],
                         cmap='viridis', alpha=0.7, s=50)
    plt.colorbar(scatter, label='Cluster')
    plt.xlabel('International Diversity Composite Score')
    plt.ylabel('Overall Excellence Score')
    plt.title('Figure 3: University Clusters Based on Diversity-Excellence Patterns',
              fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('Figure3_Cluster_Analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

    print("Publication-quality visualizations created successfully!")

def main():
    """Main statistical analysis function."""
    print("=== ADVANCED STATISTICAL ANALYSIS ===")
    print("Project: International Student Diversity and University Excellence")
    print("Author: Dr. Dharmendra Pandey, Symbiosis International (Deemed University)")
    print("=" * 70)

    # Setup visualization style
    colors = setup_visualization_style()

    # Load processed data
    df = load_processed_data()
    if df is None:
        return None

    # Conduct analyses
    print("\n1. Correlation Analysis...")
    correlations = correlation_analysis(df)

    print("\n2. Regression Analysis...")
    regressions = regression_analysis(df)

    print("\n3. Cluster Analysis...")
    clusters = cluster_analysis(df)

    print("\n4. Mediation Analysis...")
    mediation = mediation_analysis(df)

    # Compile results
    analysis_results = {
        'correlations': correlations,
        'regressions': regressions,
        'clusters': clusters,
        'mediation': mediation
    }

    # Create visualizations
    print("\n5. Creating Publication Visualizations...")
    create_publication_visualizations(df, analysis_results, colors)

    print("\n" + "=" * 70)
    print("Advanced statistical analysis completed successfully!")

    return analysis_results

if __name__ == "__main__":
    results = main()
